{% block content %}
    {% load static %}
    {% load widget_tweaks %}

    <form method="post" action="">
        {% csrf_token %}

        <div id="modalheader" class="modal-header">
            <h3 class="modal-title">Update Ticket</h3>
        </div>

        <div class="modal-body">
            <!-- Ticket Image Preview Section -->
            {% if object.image_id and object.image_id.images %}
                <div class="text-center mb-4">
                    <h5>Ticket Image</h5>
                    {% load thumbnail %}
                    <img src="{% thumbnail object.image_id.images "400x300" %}"
                         alt="Ticket Image"
                         class="img-fluid border rounded"
                         style="max-height: 300px;">
                    <p class="text-muted mt-2">Ticket #{{ object.ticket_no|default:"N/A" }}</p>
                </div>
                <hr>
            {% endif %}

            <div class="{% if form.non_field_errors %}invalid{% endif %} mb-2">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>

            {% for field in form %}
                <div class="form-group mb-3">
                    <label for="{{ field.id_for_label }}">{{ field.label }}</label>

                    {% if field.field.widget.input_type == "checkbox" %}
                        <div class="form-check">
                            {% render_field field class="form-check-input" %}
                        </div>
                    {% else %}
                        {% render_field field class="form-control" placeholder=field.label %}
                    {% endif %}

                    <div class="{% if field.errors %}invalid{% endif %}">
                        {% for error in field.errors %}
                            <p class="help-block text-danger">{{ error }}</p>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}

        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn update-count btn-primary">Update</button>
        </div>

    </form>
{% endblock content %}
